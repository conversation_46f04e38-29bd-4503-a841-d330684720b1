'use client'

import { useRef, useEffect, useState } from 'react'
import Editor, { OnMount } from '@monaco-editor/react'
import { editor as monacoEditor } from 'monaco-editor'
import { useMonacoCollaboration } from '@/hooks/use-monaco-collaboration'
import { CollaborationIndicator } from '@/components/collaboration/collaboration-indicator'
import { useTheme } from 'next-themes'
import { logger } from '@/lib/services/logger'
import type { UserSubscription } from '@/lib/subscription'

interface CollaborativeMonacoEditorProps {
  value: string
  onChange?: (value: string) => void
  language?: string
  height?: string | number
  options?: monacoEditor.IStandaloneEditorConstructionOptions
  // Collaboration props
  projectId: string
  documentId: string
  userId: string
  userName: string
  userEmail: string
  subscription: UserSubscription | null
  enableCollaboration?: boolean
  onSave?: (value: string) => void
}

export function CollaborativeMonacoEditor({
  value,
  onChange,
  language = 'markdown',
  height = '100%',
  options,
  projectId,
  documentId,
  userId,
  userName,
  userEmail,
  subscription,
  enableCollaboration = true,
  onSave
}: CollaborativeMonacoEditorProps) {
  const editorRef = useRef<monacoEditor.IStandaloneCodeEditor | null>(null)
  const { theme } = useTheme()
  const [isCollaborationEnabled, setIsCollaborationEnabled] = useState(enableCollaboration)
  
  // Generate session ID from project and document
  const sessionId = `${projectId}-${documentId}`
  
  // Use collaboration hook
  const { isConnected, collaborators } = useMonacoCollaboration(editorRef.current, {
    sessionId,
    userId,
    userName,
    userEmail,
    subscription,
    enabled: isCollaborationEnabled
  })
  
  const handleEditorDidMount: OnMount = (editor, monaco) => {
    editorRef.current = editor
    
    // Configure editor options
    editor.updateOptions({
      fontSize: 14,
      fontFamily: "'Fira Code', 'Cascadia Code', 'JetBrains Mono', monospace",
      fontLigatures: true,
      lineNumbers: 'on',
      minimap: { enabled: false },
      scrollBeyondLastLine: false,
      wordWrap: 'on',
      formatOnPaste: true,
      formatOnType: true,
      automaticLayout: true,
      ...options
    })
    
    // Register save command
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
      const currentValue = editor.getValue()
      onSave?.(currentValue)
    })
    
    // Set up custom theme
    monaco.editor.defineTheme('bookscribe-light', {
      base: 'vs',
      inherit: true,
      rules: [
        { token: 'comment', foreground: '6A737D' },
        { token: 'keyword', foreground: 'D73A49' },
        { token: 'string', foreground: '032F62' },
        { token: 'number', foreground: '005CC5' },
        { token: 'type', foreground: '6F42C1' }
      ],
      colors: {
        'editor.background': '#FDFBF7',
        'editor.foreground': '#24292E',
        'editor.lineHighlightBackground': '#F6F8FA',
        'editor.selectionBackground': '#E1E4E8',
        'editorCursor.foreground': '#24292E',
        'editorWhitespace.foreground': '#D1D5DA'
      }
    })
    
    monaco.editor.defineTheme('bookscribe-dark', {
      base: 'vs-dark',
      inherit: true,
      rules: [
        { token: 'comment', foreground: '8B949E' },
        { token: 'keyword', foreground: 'FF7B72' },
        { token: 'string', foreground: 'A5D6FF' },
        { token: 'number', foreground: '79C0FF' },
        { token: 'type', foreground: 'D2A8FF' }
      ],
      colors: {
        'editor.background': '#0D1117',
        'editor.foreground': '#C9D1D9',
        'editor.lineHighlightBackground': '#161B22',
        'editor.selectionBackground': '#3B5F8A',
        'editorCursor.foreground': '#C9D1D9',
        'editorWhitespace.foreground': '#484F58'
      }
    })
    
    // Apply theme
    monaco.editor.setTheme(theme === 'dark' ? 'bookscribe-dark' : 'bookscribe-light')
  }
  
  const handleToggleCollaboration = (enabled: boolean) => {
    setIsCollaborationEnabled(enabled)
    logger.info('Collaboration toggled', { enabled, sessionId })
  }
  
  const handleInviteUser = () => {
    // This would open a modal or navigate to team management
    logger.info('Invite user clicked')
  }
  
  return (
    <div className="relative h-full flex flex-col">
      {/* Collaboration Toolbar */}
      <div className="flex items-center justify-between px-4 py-2 border-b bg-background/95 backdrop-blur">
        <div className="flex items-center gap-4">
          <span className="text-sm text-muted-foreground">
            {language === 'markdown' ? 'Markdown' : language.toUpperCase()} Editor
          </span>
          <div className="h-4 w-px bg-border" />
          <span className="text-xs text-muted-foreground">
            {value.length} characters
          </span>
        </div>
        
        <CollaborationIndicator
          sessionId={sessionId}
          isConnected={isConnected}
          collaborators={collaborators}
          currentUserId={userId}
          canInvite={true}
          onToggleCollaboration={handleToggleCollaboration}
          onInviteUser={handleInviteUser}
        />
      </div>
      
      {/* Monaco Editor */}
      <div className="flex-1 relative">
        <Editor
          height={height}
          language={language}
          value={value}
          onChange={(newValue) => onChange?.(newValue || '')}
          onMount={handleEditorDidMount}
          theme={theme === 'dark' ? 'bookscribe-dark' : 'bookscribe-light'}
          options={{
            minimap: { enabled: false },
            fontSize: 14,
            lineNumbers: 'on',
            scrollBeyondLastLine: false,
            wordWrap: 'on',
            automaticLayout: true,
            ...options
          }}
        />
        
        {/* Collaboration Status Overlay */}
        {!isConnected && isCollaborationEnabled && (
          <div className="absolute top-2 right-2 bg-yellow-500/10 text-yellow-600 dark:text-yellow-400 px-3 py-1.5 rounded-md text-xs font-medium">
            Connecting to collaboration server...
          </div>
        )}
      </div>
    </div>
  )
}